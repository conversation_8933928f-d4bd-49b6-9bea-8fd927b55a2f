# Python dependencies for Wellbot Bridge Health Endpoint Testing
# Install with: pip install -r requirements.txt

# Core HTTP client library for making requests
requests>=2.31.0

# Optional: Enhanced testing capabilities
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Optional: For advanced reporting and analysis
pandas>=2.0.0
matplotlib>=3.7.0

# Optional: For colored terminal output
colorama>=0.4.6

# Optional: For JSON schema validation
jsonschema>=4.17.0

# Optional: For performance profiling
psutil>=5.9.0
